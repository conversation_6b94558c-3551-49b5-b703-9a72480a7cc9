<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"
    />
    <meta
      http-equiv="Cross-Origin-Opener-Policy"
      content="same-origin-allow-popups"
    />
    <meta http-equiv="Cross-Origin-Embedder-Policy" content="unsafe-none" />
    <title>OpenStreetMap 小精靈遊戲</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cubic+11&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      html {
        height: 100%;
        /* 使用 vh 單位確保適配手機螢幕 */
        height: 100vh;
        /* 支援 iOS Safari 的安全區域 */
        height: 100dvh; /* 動態視窗高度 */
      }
      body {
        font-family: "Cubic 11", cursive;
        background: #000;
        overflow: hidden;
        color: #fff;
        height: 100%;
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
      }

      /* FPS 右上角顯示 */
      .fps-overlay {
        position: absolute;
        top: 10px;
        right: 12px;
        z-index: 3000;
        padding: 6px 8px;
        background: rgba(0, 0, 0, 0.65);
        border: 1px solid #555;
        color: #0f0;
        font-family: "Cubic 11", cursive;
        font-size: 12px;
        user-select: none;
      }

      .game-container {
        position: relative;
        width: 100vw;
        height: 100vh;
        height: 100dvh; /* 使用動態視窗高度 */
        background-color: #000; /* 确保背景是黑色的 */
        overflow: hidden; /* 防止內容溢出 */

        /* 使用 Flexbox 来让我们的游戏视口居中 */
        display: flex;
        justify-content: center;
        align-items: center;
      }
      #game-viewport {
        position: relative; /* 为内部的 #map 提供定位上下文 */
        width: 100%;
        height: 100%;
        max-width: 100vw;
        max-height: 100vh;

        /* *** 核心：设定固定的宽高比 *** */
        /* 例如，我们设定一个 16:9 的宽高比 */
        aspect-ratio: 16 / 9;

        /* 
          *  为了处理极端情况（例如一个非常窄的屏幕），
          *  我们需要确保视口本身不会超出屏幕。
          *  这可以通过 @media 查询或更复杂的 JS 来实现，
          *  但 aspect-ratio 通常能处理好大部分情况。
          */
      }

      /* 3. 修改 #map 的样式 */
      #map {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        /* 其他样式保持不变 */
      }

      #map.fading-to-black {
        filter: brightness(0%) grayscale(100%);
      }

      #startScreenMap {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 998;
        background-color: #000;
        filter: brightness(0.35) grayscale(0.1);
        transition: opacity 0.5s ease-in-out;
        opacity: 1;
      }

      .screen-overlay {
        /* 通用全螢幕覆蓋層樣式 */
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        text-align: center;
        overflow-y: auto;
        box-sizing: border-box;
      }

      .start-screen {
        background: rgba(0, 0, 0, 0.3);
        z-index: 1000;
      }

      .map-selection-screen {
        background: rgba(0, 0, 0, 0.85);
        z-index: 1001;
        display: none;
      }
      .map-selection-screen h2 {
        font-size: clamp(2rem, 6vw, 3.5rem);
        color: #ffff00;
        text-shadow: 2px 2px 0px #ff0000;
        margin-bottom: 2rem;
      }

      .game-title {
        font-size: clamp(2.5rem, 8vw, 4.5rem);
        color: #ffff00;
        text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #ffff00,
          0 0 20px #ffff00, 0 0 25px #ffff00, 0 0 30px #ff0000, 0 0 35px #ff0000;
        margin-bottom: 1.5rem;
        animation: gameTitlePulse 2s infinite ease-in-out;
        letter-spacing: 0.1em;
      }

      @keyframes gameTitlePulse {
        0%,
        100% {
          transform: scale(1);
          opacity: 1;
        }
        50% {
          transform: scale(1.05);
          opacity: 0.85;
        }
      }

      .start-screen-actions {
        /* 主畫面按鈕容器 */
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap; /* 按鈕過多時換行 */
        margin-bottom: 1rem;
      }

      .pacman-pixel-button {
        font-family: "Cubic 11", cursive;
        background-color: #2121de;
        color: #ffff00;
        border: 3px solid #000000;
        padding: 12px 24px;
        font-size: clamp(0.9rem, 3vw, 1.2rem);
        text-transform: uppercase;
        border-radius: 0;
        box-shadow: 4px 4px 0px #000000c0;
        cursor: pointer;
        transition: transform 0.05s ease-out, box-shadow 0.05s ease-out,
          background-color 0.05s ease-out;
        margin: 10px;
        display: inline-block;
        outline: none;
        min-width: 180px;
      }
      .pacman-pixel-button:hover {
        background-color: #4242ff;
        color: #ffff66;
        box-shadow: 2px 2px 0px #000000c0;
        transform: translate(2px, 2px);
      }

      .pacman-pixel-button:active {
        background-color: #0000b3;
        box-shadow: 0px 0px 0px #000000c0;
        transform: translate(4px, 4px);
      }

      .map-button {
        margin: 8px;
      }

      .map-button.pacman-pixel-button.active {
        background-color: #ffff00;
        color: #2121de;
        border-color: #000000;
        box-shadow: 2px 2px 0px #000000c0;
        transform: translate(2px, 2px);
      }
      .map-button.pacman-pixel-button.active:hover {
        background-color: #ffff66;
        color: #0000b3;
      }

      .poi-icon-container {
        z-index: 2000 !important;
      }

      /* 1. Flexbox 布局的包裹容器 */
      .poi-icon-wrapper {
        display: flex;
        flex-direction: column; /* 垂直排列：图标在上，标题在下 */
        align-items: center; /* 水平居中所有子元素 */
      }

      /* 2. 图标 (水滴) 本身 */
      .poi-icon {
        width: 24px;
        height: 24px;
        border-radius: 50% 50% 50% 0; /* 水滴形状 */
        transform: rotate(-45deg); /* 旋转水滴 */
        border: 2px solid white;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);

        /* 使用 Flexbox 让内部的字母也居中 */
        display: flex;
        justify-content: center;
        align-items: center;
      }

      /* 3. 图标内的字母 */
      .poi-letter {
        display: block;
        transform: rotate(45deg); /* 把字母旋转回来 */
        color: white;
        font-family: "Cubic 11", cursive;
        font-size: 14px;
        font-weight: bold;
        text-shadow: 1px 1px 2px black;
      }

      /* 4. 下方的文字标签 */
      .poi-title {
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        font-family: sans-serif;
        white-space: nowrap;
        margin-top: 4px; /* 图标和标签之间的间距 */
        pointer-events: none; /* 避免遮挡鼠标事件 */
      }

      /* 5. 专属颜色 (保持不变) */
      .monument-icon {
        background-color: #dd0e0e;
      }
      .store-icon {
        background-color: #1bc431;
      }
      .park-icon {
        background-color: #006400;
      }
      .hotel-icon {
        background-color: #1bc431;
      }
      .bank-icon {
        background-color: #d1e428;
      }
      .restaurant-icon {
        background-color: #dd0e0e;
      }
      .cafe-icon {
        background-color: #1bc431;
      }
      .bubble-tea-icon {
        background-color: #1bc431;
      }
      .atm-icon {
        background-color: #d1e428;
      }

      .content-toggle-container {
        /* 包裹說明和排行榜內容的容器 */
        width: 100%;
        max-width: 600px; /* 與內容區塊寬度一致 */
        margin-top: 1rem;
      }
      .health-bar-container {
        width: 100%; /* 讓容器寬度與 .game-ui 的內容區對齊 */
        height: 14px; /* 血條的高度 */
        background-color: #333; /* 血條的背景色 (空血時的顏色) */
        border: 2px solid #555; /* 血條的邊框 */
        border-radius: 7px; /* 圓角效果 */
        padding: 2px; /* 內邊距，讓填充條和邊框之間有點空隙 */
        box-sizing: border-box; /* 確保 padding 和 border 不會增加總寬度 */
        margin: 8px 0; /* 與上下元素的間距 */
      }

      .health-bar {
        height: 100%; /* 填充條高度與容器一致 */
        width: 100%; /* 初始寬度為 100% (滿血) */
        background-color: #00ff00; /* 滿血時的顏色 (綠色) */
        border-radius: 4px; /* 填充條自己的圓角 */

        /* 這是最重要的部分：平滑的過渡動畫 */
        transition: width 0.5s ease-in-out, background-color 0.5s ease-in-out;
      }
      .instructions,
      .leaderboard {
        max-width: 600px;
        width: 100%; /* 相對於 .content-toggle-container */
        margin: 0 auto 1rem auto; /* 水平居中，底部留白 */
        padding: 20px;
        background: rgba(0, 0, 0, 0.75); /* 稍微加深一點 */
        border: 3px solid #ffff00; /* 加粗邊框 */
        border-radius: 0px;
        box-shadow: 0 0 10px rgba(255, 255, 0, 0.2);
        display: none;
      }
      .instructions h3,
      .leaderboard h3 {
        color: #ffff00;
        margin-bottom: 1rem;
        font-size: clamp(1.2rem, 5vw, 1.8rem);
        text-shadow: 2px 2px 0px #000; /* 加粗陰影 */
      }
      .instructions ul,
      .leaderboard ol {
        list-style: none;
        line-height: 1.8;
        padding-left: 0;
        font-size: clamp(0.8rem, 3vw, 1rem);
      }
      .instructions li {
        margin: 10px 0;
        padding-left: 25px;
        position: relative;
      }
      .instructions li:before {
        content: ">>";
        position: absolute;
        left: 0;
        color: #ffff00;
        font-weight: bold;
      }

      .leaderboard ol {
        padding-left: 0;
      } /* 確保排行榜列表不內縮 */
      .leaderboard li {
        counter-increment: leaderboard;
        padding: 8px 10px;
        margin: 6px 0;
        background: rgba(30, 30, 80, 0.5);
        border-radius: 0px;
        border-left: 4px solid #ffff00;
      }
      .leaderboard li:before {
        content: counter(leaderboard) ". ";
        font-weight: bold;
        color: #ffff00;
        margin-right: 10px;
      }

      .game-ui {
        position: absolute;
        top: 20px;
        left: 20px;
        z-index: 500;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px;
        border-radius: 10px;
        font-family: "Cubic 11", cursive;
        font-size: 14px;
        border: 1px solid #555;
      }
      .ui-row {
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
        min-width: 300px;
      }
      .quest-container {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #555;
        flex-direction: column; /* 让描述和进度垂直排列 */
        align-items: flex-start;
        text-align: left;
      }
      #quest-description {
        font-size: 12px;
      }
      #quest-progress {
        font-size: 14px;
        color: #ffff00; /* 进度用黄色突出显示 */
      }

      .pause-screen {
        background: rgba(10, 10, 30, 0.92); /* 深藍色調背景 */
        border: 4px solid #ffb84d; /* 鬼怪橙色邊框 */
        box-shadow: 0 0 25px #ffb84d, inset 0 0 20px rgba(0, 0, 0, 0.5);
        z-index: 2500;
        color: white;
        display: none;
      }
      .pause-screen h2 {
        font-size: clamp(2.8rem, 8vw, 4.5rem);
        color: #ff0000;
        text-shadow: 3px 3px 0px #000000, 0 0 8px #fff, 0 0 12px #ff0000,
          0 0 18px #ffb84d; /* 加入橙色光暈 */
        margin-bottom: 2.5rem;
        animation: blinkRed 0.8s infinite steps(1, end); /* 調整閃爍速度 */
      }
      #minimap-container {
        position: fixed; /* 改為 fixed，相對於視窗定位 */
        bottom: 20px;
        right: 20px;
        width: 200px; /* 小地图宽度 */
        height: 200px; /* 小地图高度 */
        z-index: 1000; /* 确保它在游戏 UI 之上 */
        border: 3px solid #ffff00;
        box-shadow: 0 0 15px rgba(255, 255, 0, 0.5);
        background-color: #111;
        border-radius: 5px; /* 轻微的圆角 */
        overflow: hidden; /* 隐藏地图超出部分 */
        display: none;
      }
      #minimap {
        width: 100%;
        height: 100%;
      }
      .minimap-player-icon {
        background-color: #ffff00; /* 黄色 */
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 8px #ffff00;
      }
      .minimap-poi-icon {
          border-radius: 50%;
          border: 1px solid rgba(255, 255, 255, 0.7);
          box-shadow: 0 0 3px black;
          opacity: 0.8;
      }
      .minimap-poi-icon.monument-icon { background-color: #dd0e0e; }
      .minimap-poi-icon.store-icon { background-color: #1bc431; }
      .minimap-poi-icon.park-icon { background-color: #006400; }
      .minimap-poi-icon.hotel-icon { background-color: #1bc431; }
      .minimap-poi-icon.bank-icon { background-color: #d1e428; }
      .minimap-poi-icon.restaurant-icon { background-color: #dd0e0e; }
      .minimap-poi-icon.cafe-icon { background-color: #1bc431; }
      .minimap-poi-icon.bubble-tea-icon { background-color: #1bc431; }
      .minimap-poi-icon.atm-icon { background-color: #d1e428; }

      .minimap-poi-icon.quest-target {
          transform: scale(1.8); /* 放大 */
          border: 2px solid white;
          opacity: 1;
          z-index: 1000 !important; /* 确保高亮图标在最顶层 */
          
          /* 添加一个发光的动画 */
          animation: pulse 1.5s infinite ease-in-out;
      }

      .minimap-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          
          display: flex;
          flex-direction: column; /* 垂直排列 */
          justify-content: center;
          align-items: center;
          
          padding: 4px 0;
          background-color: rgba(0, 0, 0, 0.6); /* 半透明黑色背景 */
          color: white;
          
          font-family: 'Cubic 11', cursive;
          text-shadow: 1px 1px 2px black;
          pointer-events: none; /* 确保它不会影响地图的鼠标交互 */

          z-index: 2000;

          /* 默认隐藏，我们用 JS 控制显示 */
          display: none;
      }

      #minimap-timer-label {
          font-size: 12px;
          color: #ccc;
      }

      #minimap-timer-countdown {
          font-size: 24px;
          color: #ffff00; /* 倒计时用黄色突出 */
          font-weight: bold;
      }

      @keyframes blinkRed {
        /* 調整閃爍效果 */
        0%,
        100% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.6;
          transform: scale(1.02);
        }
      }
      .pause-screen .pacman-pixel-button {
        margin: 15px;
      }

      .game-over-screen {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: none;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 2500;
        color: white;
      }
      .map-selector {
        margin: 20px 0;
      } /* 用於地圖選擇界面中的按鈕組 */
      .countdown {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 5rem;
        color: #ffff00;
        text-shadow: 3px 3px 0px #ff6b6b;
        z-index: 600;
        display: none;
      }
      .pacman-icon {
        width: 24px;
        height: 24px;
        background-color: #ffff00;
        border-radius: 50%;
        position: relative;
        transition: transform 0.05s linear, opacity 0.3s ease-out;
      }
      .pacman-icon.hidden {
        opacity: 0;
        pointer-events: none;
      }
      .pacman-icon::before {
        content: "";
        position: absolute;
        width: 0;
        height: 0;
        top: 50%;
        left: 12px;
        transform: translateY(-50%);
        border-style: solid;
        border-color: transparent #111 transparent transparent;
        animation: pacman-mouth-chomp 0.4s infinite;
      }
      @keyframes pacman-mouth-chomp {
        0%,
        100% {
          border-width: 12px 12px 12px 0;
        }
        50% {
          border-width: 4px 12px 4px 0;
        }
      }
      .pacman-icon.facing-true-left {
        transform: rotate(180deg);
      }
      .pacman-icon.facing-true-right {
        transform: rotate(0deg);
      }
      .pacman-icon.facing-true-up {
        transform: rotate(-90deg);
      }
      .pacman-icon.facing-true-down {
        transform: rotate(90deg);
      }
      .ghost-icon {
        width: 20px;
        height: 20px;
        border-radius: 10px 10px 0 0;
        position: relative;
        overflow: visible;
      }
      .ghost-icon::before,
      .ghost-icon::after {
        content: "";
        position: absolute;
        width: 6px;
        height: 8px;
        background-color: white;
        border-radius: 50%;
        top: 4px;
        border: 1px solid #555;
      }
      .ghost-icon::before {
        left: 3px;
      }
      .ghost-icon::after {
        right: 3px;
      }
      .ghost-icon > div.wave1,
      .ghost-icon > div.wave2,
      .ghost-icon > div.wave3 {
        position: absolute;
        bottom: -6px;
        width: 33.33%;
        height: 6px;
        background-color: inherit;
        border-radius: 0 0 50% 50% / 0 0 100% 100%;
      }
      .ghost-icon > div.wave1 {
        left: 0;
      }
      .ghost-icon > div.wave2 {
        left: 33.33%;
      }
      .ghost-icon > div.wave3 {
        left: 66.66%;
      }
      .ghost-red {
        background: #ff0000;
      }
      .ghost-pink {
        background: #ffc0cb;
      }
      .ghost-cyan {
        background: #00ffff;
      }
      .ghost-orange {
        background: #ffb84d;
      }
      .ghost-purple {
        background: #800080;
      }
      .ghost-green {
        background: #008000;
      }
      .ghost-blue {
        background: #0000ff;
      }
      .ghost-scared {
        background: #2222dd;
      }
      .ghost-scared::before,
      .ghost-scared::after {
        background-color: white;
        width: 8px;
        height: 4px;
        top: 7px;
      }
      @keyframes ghost-eaten-fade {
        0% {
          opacity: 1;
          transform: scale(1);
        }
        100% {
          opacity: 0;
          transform: scale(0.5) translateY(-15px);
        }
      }
      .ghost-icon.ghost-eaten {
        animation: ghost-eaten-fade 0.5s forwards;
      }
      .wasted-screen-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 2000;
        transition: background-color 2.5s ease-in-out;
      }
      .wasted-screen-overlay.active {
        display: flex;
        background-color: rgba(0, 0, 0, 1);
      }
      .wasted-banner {
        width: 100%;
        background-color: #000000;
        padding: 20px 0;
        text-align: center;
        opacity: 0;
        transform: scale(1) translateY(0);
      }
      .wasted-text {
        font-family: "Impact", Haettenschweiler, "Arial Narrow Bold", sans-serif;
        font-size: 6rem;
        color: #d32f2f;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        text-shadow: 2px 2px 0 #000, -1px -1px 0 #000, 1px -1px 0 #000,
          -1px 1px 0 #000, 1px 1px 0 #000, 3px 3px 5px rgba(0, 0, 0, 0.5);
      }
      .dot {
        width: 4px;
        height: 4px;
        background: #ffff00;
        border-radius: 50%;
      }
      .power-pellet {
        width: 12px;
        height: 12px;
        background: #ffff00;
        border-radius: 50%;
        animation: blink 1s infinite;
      }
      @keyframes blink {
        0%,
        50% {
          opacity: 1;
        }
        51%,
        100% {
          opacity: 0.3;
        }
      }
      .food-icon {
        font-size: 20px;
        text-align: center;
        line-height: 24px;
        text-shadow: 0 0 5px white; /* 给 emoji 加一点光晕 */
      }
      .backpack-container {
        position: absolute;
        bottom: 20px;
        left: 20px; /* 放在左下角 */
        display: flex; /* 横向排列 */
        gap: 10px; /* 格子之间的间距 */
        z-index: 1000;
        display: none;
      }
      .backpack-slot {
        width: 60px;
        height: 60px;
        background-color: rgba(0, 0, 0, 0.7);
        border: 2px solid #555;
        border-radius: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        font-size: 28px; /* emoji 的大小 */
      }
      .slot-key {
        position: absolute;
        top: 2px;
        left: 4px;
        font-size: 12px;
        color: #ccc;
        font-family: "Cubic 11", cursive;
      }
      .backpack-slot.filled {
        border-color: #ffff00;
      }
      .dev-console {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.85);
        color: #0f0;
        font-family: "Courier New", Courier, monospace;
        font-size: 14px;
        padding: 10px;
        z-index: 3000;
        display: none;
        border-top: 2px solid #0f0;
      }
      .dev-console-output {
        height: 100px;
        overflow-y: auto;
        margin-bottom: 10px;
        border: 1px solid #0a0;
        padding: 5px;
        white-space: pre-wrap;
      }
      .dev-console-input {
        width: calc(100% - 20px);
        background-color: #111;
        color: #0f0;
        border: 1px solid #0a0;
        padding: 5px;
        font-family: "Courier New", Courier, monospace;
        font-size: 14px;
      }
      .dev-console-input:focus {
        outline: none;
        border-color: #0f0;
      }

      /* 用戶認證區域樣式 */
      .user-auth-section {
        margin: 20px 0;
        padding: 20px;
        background: rgba(0, 0, 0, 0.7);
        border: 2px solid #ffff00;
        border-radius: 10px;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
      }

      .login-prompt {
        text-align: center;
      }

      .login-prompt p {
        color: #fff;
        margin-bottom: 15px;
        font-size: clamp(0.9rem, 3vw, 1.1rem);
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 10px;
        background: rgba(255, 255, 0, 0.1);
        border-radius: 8px;
      }

      .user-avatar img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 2px solid #ffff00;
      }

      .user-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .user-details span {
        color: #ffff00;
        font-weight: bold;
        font-size: 1.1rem;
      }

      .logout-btn {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        align-self: flex-start;
      }

      .logout-btn:hover {
        background-color: #c82333;
      }

      /* 排行榜樣式優化 */
      .leaderboard-entry {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 0, 0.2);
      }

      .leaderboard-entry:last-child {
        border-bottom: none;
      }

      .rank {
        font-weight: bold;
        color: #ffff00;
        min-width: 40px;
      }

      .player-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        margin: 0 10px;
      }

      .player-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 1px solid #ffff00;
      }

      .player-name {
        color: #fff;
        font-weight: normal;
      }

      .score-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        text-align: right;
      }

      .score {
        color: #ffff00;
        font-weight: bold;
      }

      .map-name {
        color: #ccc;
        font-size: 0.8rem;
      }

      /* 本地記錄樣式 */
      .local-entry {
        background: rgba(255, 149, 0, 0.1);
        border-left: 3px solid #ff9500;
      }

      .local-entry .rank {
        color: #ff9500;
      }

      .local-entry .score {
        color: #ff9500;
      }

      /* 登入提示樣式 */
      .high-score-login-prompt {
        color: white;
      }

      .high-score-login-prompt h3 {
        color: #ffff00;
        margin-bottom: 15px;
      }

      .high-score-login-prompt ul {
        text-align: left;
        margin: 15px 0;
        padding-left: 20px;
      }

      .high-score-login-prompt li {
        margin: 8px 0;
        color: #ccc;
      }

      /* 遷移提示樣式 */
      .migration-prompt .pacman-pixel-button {
        margin: 5px;
        min-width: 120px;
      }

      /* 手機控制樣式 */
      .mobile-dpad {
        position: fixed;
        bottom: calc(env(safe-area-inset-bottom) + 15px);
        right: calc(env(safe-area-inset-right) + 15px);
        width: 140px; /* 縮小虛擬方向鍵 */
        height: 140px; /* 縮小虛擬方向鍵 */
        z-index: 9999;
        display: none; /* 預設隱藏，由 JS 控制顯示 */
      }

      .mobile-device .mobile-dpad {
        display: block;
      }

      .dpad-button {
        position: absolute;
        width: 44px; /* 縮小按鈕 */
        height: 44px; /* 縮小按鈕 */
        border: 2px solid #ffff00;
        background: rgba(0, 0, 0, 0.8);
        color: #ffff00;
        font-size: 16px; /* 縮小字體 */
        font-weight: bold;
        border-radius: 8px;
        cursor: pointer;
        user-select: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.1s ease;
        font-family: "Cubic 11", cursive;
      }

      .dpad-button:active,
      .dpad-button.active {
        background: rgba(255, 255, 0, 0.3);
        transform: scale(0.95);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .dpad-up {
        top: 0;
        left: 48px; /* 調整位置適應新尺寸 */
      }

      .dpad-down {
        bottom: 0;
        left: 48px; /* 調整位置適應新尺寸 */
      }

      .dpad-left {
        top: 48px; /* 調整位置適應新尺寸 */
        left: 0;
      }

      .dpad-right {
        top: 48px; /* 調整位置適應新尺寸 */
        right: 0;
      }

      .dpad-center {
        top: 48px; /* 調整位置適應新尺寸 */
        left: 48px; /* 調整位置適應新尺寸 */
        background: rgba(255, 0, 0, 0.8);
        border-color: #ff0000;
        color: #ffffff;
      }

      .dpad-center:active,
      .dpad-center.active {
        background: rgba(255, 0, 0, 0.6);
      }

      /* 觸控回饋動畫 */
      @keyframes buttonPress {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(0.95);
        }
        100% {
          transform: scale(1);
        }
      }

      .dpad-button.pressed {
        animation: buttonPress 0.1s ease;
      }

      /* 手機專用的觸控優化 */
      .mobile-device .dpad-button {
        /* 增加觸控區域 */
        padding: 2px;
        /* 防止選取文字 */
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        /* 防止觸控時的高亮 */
        -webkit-tap-highlight-color: transparent;
        /* 防止長按選單 */
        -webkit-touch-callout: none;
      }

      /* 觸控狀態指示器 */
      .touch-indicator {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 255, 255, 0.8);
        color: #000;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 10000;
        display: none;
      }

      .mobile-device .touch-indicator {
        display: block;
      }

      /* 手機專用 UI 調整 */
      @media (max-width: 768px) {
        /* 確保所有螢幕覆蓋層適配手機螢幕 */
        .screen-overlay {
          height: 100vh;
          height: 100dvh;
          max-height: 100vh;
          max-height: 100dvh;
        }
        .mobile-device .fps-overlay {
          top: 10px;
          left: 10px;
          right: auto;
          font-size: 10px;
        }

        .mobile-device .dev-console {
          display: none; /* 手機上隱藏開發者控制台 */
        }

        .mobile-device #gameOverScreen,
        .mobile-device #startScreen {
          padding: 10px;
        }

        .mobile-device .pacman-pixel-button {
          min-height: 44px; /* 觸控友善的最小尺寸 */
          padding: 12px 20px;
          font-size: 16px;
        }

        .mobile-device .leaderboard-entry {
          padding: 12px;
          font-size: 14px;
        }

        /* 手機版物品欄調整 - 垂直堆疊布局 */
        .mobile-device .backpack-container {
          bottom: calc(env(safe-area-inset-bottom) + 15px); /* 更貼近底部 */
          left: 10px; /* 與小地圖對齊左側 */
          gap: 6px; /* 縮小間距 */
          flex-direction: row; /* 確保水平排列 */
        }

        .mobile-device .backpack-slot {
          width: 45px; /* 縮小物品欄格子 */
          height: 45px; /* 縮小物品欄格子 */
        }

        .mobile-device .backpack-slot .slot-key {
          font-size: 10px; /* 縮小按鍵提示 */
        }

        .mobile-device .backpack-slot .slot-item {
          font-size: 16px; /* 調整物品圖示大小 */
        }

        /* 響應式縮放 - 超小螢幕 */
        @media (max-width: 480px) and (max-height: 640px) {
          .mobile-device #minimap-container {
            width: 70px; /* 進一步縮小 */
            height: 70px;
            bottom: calc(
              env(safe-area-inset-bottom) + 60px
            ); /* 調整間距，更貼近底部 */
          }

          .mobile-device .backpack-slot {
            width: 35px; /* 進一步縮小 */
            height: 35px;
          }

          .mobile-device .backpack-slot .slot-key {
            font-size: 8px;
          }

          .mobile-device .backpack-slot .slot-item {
            font-size: 14px;
          }

          .mobile-dpad {
            width: 120px; /* 縮小虛擬方向鍵 */
            height: 120px;
          }

          .dpad-button {
            width: 38px;
            height: 38px;
            font-size: 14px;
          }

          .dpad-up,
          .dpad-down {
            left: 41px;
          }

          .dpad-left,
          .dpad-right,
          .dpad-center {
            top: 41px;
          }

          .dpad-center {
            left: 41px;
          }
        }

        /* 手機版小地圖調整 - 垂直堆疊布局 */
        .mobile-device #minimap-container {
          width: 90px; /* 適中大小 */
          height: 90px;
          bottom: calc(
            env(safe-area-inset-bottom) + 70px
          ); /* 在物品欄上方，更貼近底部 */
          left: 10px; /* 左下角位置 */
          right: auto;
        }

        /* 手機版遊戲結算畫面調整 */
        .mobile-device .game-over-screen {
          padding: 20px 15px;
          padding-bottom: calc(env(safe-area-inset-bottom) + 20px);
          box-sizing: border-box;
          max-height: 100vh;
          max-height: 100dvh;
          overflow-y: auto;
        }

        .mobile-device .game-over-screen h2 {
          font-size: 1.8rem;
          margin-bottom: 15px;
        }

        .mobile-device .game-over-screen .ui-row {
          margin: 15px 0 !important;
        }

        .mobile-device .game-over-screen .ui-row span {
          font-size: 1.4rem !important;
        }

        .mobile-device #newHighScore {
          font-size: 1.2rem !important;
          margin: 8px 0 !important;
        }

        .mobile-device .game-over-screen .pacman-pixel-button {
          margin: 8px 5px;
          width: calc(50% - 10px);
          display: inline-block;
          text-align: center;
        }
      }

      /* 橫向模式調整 */
      @media (max-width: 768px) and (orientation: landscape) {
        .mobile-dpad {
          bottom: calc(env(safe-area-inset-bottom) + 10px);
          right: calc(env(safe-area-inset-right) + 10px);
          width: 140px;
          height: 140px;
        }

        .dpad-button {
          width: 45px;
          height: 45px;
          font-size: 16px;
        }

        .dpad-up {
          left: 47.5px;
        }

        .dpad-down {
          left: 47.5px;
        }

        .dpad-left {
          top: 47.5px;
        }

        .dpad-right {
          top: 47.5px;
        }

        .dpad-center {
          top: 47.5px;
          left: 47.5px;
        }

        /* 橫向模式調整 - 也使用垂直堆疊 */
        .mobile-device #minimap-container {
          width: 70px; /* 橫向模式縮小 */
          height: 70px;
          bottom: calc(
            env(safe-area-inset-bottom) + 55px
          ); /* 在物品欄上方，更貼近底部 */
          left: 5px;
          right: auto;
        }

        /* 橫向模式物品欄調整 */
        .mobile-device .backpack-container {
          bottom: calc(env(safe-area-inset-bottom) + 10px); /* 更貼近底部 */
          left: 5px; /* 與小地圖對齊 */
        }

        .mobile-device .backpack-slot {
          width: 35px; /* 橫向模式更小 */
          height: 35px;
        }

        .mobile-device .backpack-slot .slot-key {
          font-size: 8px;
        }

        .mobile-device .backpack-slot .slot-item {
          font-size: 12px;
        }
      }

      /* 隱藏桌面專用元素 */
      .mobile-device .desktop-only {
        display: none !important;
      }

      .mobile-device .keyboard-hint {
        display: none !important;
      }

      /* 設定介面樣式 */
      .settings-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      }

      .settings-content {
        background: #1a1a1a;
        border: 3px solid #ffff00;
        border-radius: 10px;
        padding: 20px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 0 20px rgba(255, 255, 0, 0.3);
      }

      .settings-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 2px solid #333;
        padding-bottom: 10px;
      }

      .settings-header h2 {
        color: #ffff00;
        margin: 0;
        font-family: "Cubic 11", cursive;
      }

      .close-btn {
        background: none;
        border: none;
        color: #ffff00;
        font-size: 24px;
        cursor: pointer;
        padding: 5px;
        border-radius: 3px;
        transition: background 0.2s;
      }

      .close-btn:hover {
        background: rgba(255, 255, 0, 0.2);
      }

      .settings-body {
        margin-bottom: 20px;
      }

      .setting-item {
        margin-bottom: 20px;
        padding: 15px;
        background: #2a2a2a;
        border-radius: 5px;
        border: 1px solid #444;
      }

      .setting-label {
        display: flex;
        align-items: center;
        color: #fff;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
        cursor: pointer;
        font-family: "Cubic 11", cursive;
      }

      .setting-label input[type="checkbox"] {
        display: none;
      }

      .checkmark {
        width: 20px;
        height: 20px;
        border: 2px solid #ffff00;
        border-radius: 3px;
        margin-right: 10px;
        position: relative;
        background: #000;
        transition: all 0.2s;
      }

      .setting-label input[type="checkbox"]:checked + .checkmark {
        background: #ffff00;
      }

      .setting-label input[type="checkbox"]:checked + .checkmark::after {
        content: "✓";
        position: absolute;
        top: -2px;
        left: 2px;
        color: #000;
        font-weight: bold;
        font-size: 14px;
      }

      .setting-description {
        color: #ccc;
        font-size: 12px;
        margin-top: 5px;
        line-height: 1.4;
      }

      .volume-control {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
      }

      .volume-control input[type="range"] {
        flex: 1;
        height: 6px;
        background: #444;
        border-radius: 3px;
        outline: none;
        -webkit-appearance: none;
      }

      .volume-control input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 20px;
        height: 20px;
        background: #ffff00;
        border-radius: 50%;
        cursor: pointer;
      }

      .volume-control input[type="range"]::-moz-range-thumb {
        width: 20px;
        height: 20px;
        background: #ffff00;
        border-radius: 50%;
        cursor: pointer;
        border: none;
      }

      .volume-control input[type="range"]:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      #volumeValue {
        color: #ffff00;
        font-weight: bold;
        min-width: 40px;
        text-align: center;
      }

      .settings-footer {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        border-top: 2px solid #333;
        padding-top: 15px;
      }

      .settings-footer .pacman-pixel-button {
        flex: 1;
        margin: 0;
      }

      /* 遊戲中設定按鈕樣式 */
      .game-settings-btn {
        background: rgba(0, 0, 0, 0.8);
        color: #ffff00;
        border: 2px solid #ffff00;
        padding: 8px 16px;
        border-radius: 5px;
        cursor: pointer;
        font-family: "Cubic 11", cursive;
        font-size: 14px;
        transition: all 0.2s ease;
      }

      .game-settings-btn:hover {
        background: rgba(255, 255, 0, 0.2);
        transform: scale(1.05);
      }

      .game-settings-btn:active {
        transform: scale(0.95);
      }

      /* 手機版設定介面調整 */
      @media (max-width: 768px) {
        .settings-content {
          width: 95%;
          padding: 15px;
          max-height: 90vh;
        }

        .settings-header h2 {
          font-size: 1.2rem;
        }

        .setting-item {
          padding: 12px;
          margin-bottom: 15px;
        }

        .setting-label {
          font-size: 14px;
        }

        .settings-footer {
          flex-direction: column;
          gap: 8px;
        }

        .settings-footer .pacman-pixel-button {
          width: 100%;
          margin: 0;
        }

        .mobile-device .game-settings-btn {
          padding: 12px 20px;
          font-size: 16px;
          min-height: 44px; /* 觸控友善尺寸 */
        }
      }
    </style>
  </head>
  <body>
    <div class="game-container">
      <div id="startScreenMap"></div>
      <div id="map"></div>

      <div class="wasted-screen-overlay" id="wastedScreenOverlay">
        <div class="wasted-banner" id="wastedBanner">
          <span class="wasted-text">WASTED</span>
        </div>
      </div>

      <div id="backpack-ui" class="backpack-container">
        <div class="backpack-slot" id="slot-0">
          <span class="slot-key">1</span>
          <span class="slot-item"></span>
        </div>
        <div class="backpack-slot" id="slot-1">
          <span class="slot-key">2</span>
          <span class="slot-item"></span>
        </div>
        <div class="backpack-slot" id="slot-2">
          <span class="slot-key">3</span>
          <span class="slot-item"></span>
        </div>
      </div>

      <div class="countdown" id="countdown"></div>

      <div class="game-ui" id="gameUI" style="display: none">
        <div class="ui-row">
          <span>分數: <span id="score">0</span></span>
          <span>生命: <span id="lives">3</span></span>
        </div>
        <div class="health-bar-container">
          <div id="healthBar" class="health-bar"></div>
        </div>
        <div class="ui-row">
          <span>關卡: <span id="level">1</span></span>
          <span>時間: <span id="timer">5:00</span></span>
        </div>
        <div class="ui-row">
          <span>剩餘點數: <span id="dotsLeft">0</span></span>
          <span>最高分: <span id="highScore">0</span></span>
        </div>

        <!-- 遊戲中設定按鈕 -->
        <div class="ui-row">
          <button
            class="game-settings-btn"
            id="gameSettingsBtn"
            onclick="window.gameSettings?.showSettingsModal()"
          >
            ⚙️ 設定
          </button>
        </div>

        <!-- *** 新增：任务显示区域 *** -->
        <div id="quest-display" class="ui-row quest-container">
          <span id="quest-description"></span>
          <span id="quest-progress"></span>
        </div>
      </div>

      <div class="screen-overlay start-screen" id="startScreen">
        <h1 class="game-title">PAC-MAP</h1>

        <!-- 用戶登入區域 -->
        <div class="user-auth-section" id="userAuthSection">
          <!-- 未登入狀態 -->
          <div class="login-prompt" id="loginPrompt">
            <p>登入以保存您的遊戲記錄和排行榜</p>
            <div
              id="g_id_onload"
              data-client_id="225638092115-2rjm7fr2me7k9k420v92m329sk6s3ho0.apps.googleusercontent.com"
              data-callback="handleGoogleLogin"
              data-auto_prompt="false"
              data-cancel_on_tap_outside="false"
              data-itp_support="true"
              data-use_fedcm_for_prompt="true"
            ></div>
            <div
              class="g_id_signin"
              data-type="standard"
              data-size="large"
              data-theme="outline"
              data-text="sign_in_with"
              data-shape="rectangular"
              data-logo_alignment="left"
            ></div>
          </div>

          <!-- 已登入狀態 -->
          <div class="user-info" id="userInfo" style="display: none">
            <div class="user-avatar">
              <img id="userAvatar" src="" alt="用戶頭像" />
            </div>
            <div class="user-details">
              <span id="userName">用戶名稱</span>
              <button class="logout-btn" id="logoutBtn">登出</button>
            </div>
          </div>
        </div>

        <div class="start-screen-actions">
          <button class="pacman-pixel-button" id="startGameBtn">
            開始遊戲
          </button>
          <button class="pacman-pixel-button" id="instructionsBtn">
            遊戲說明
          </button>
          <button class="pacman-pixel-button" id="leaderboardBtn">
            排行榜
          </button>
          <button class="pacman-pixel-button" id="toggleControlBtn">
            📱 切換控制模式
          </button>
          <button class="pacman-pixel-button" id="settingsBtn">
            ⚙️ 遊戲設定
          </button>
        </div>
        <div class="content-toggle-container">
          <div class="instructions" id="instructionsContent">
            <h3>遊戲說明</h3>
            <ul>
              <li>使用 WASD 或方向鍵控制小精靈移動</li>
              <li>空白鍵暫停遊戲</li>
              <li>收集黃色點數 (20分) 和大力丸 (50分)</li>
              <li>吃大力丸後可以擊殺鬼怪 (150分)</li>
              <li>避免被鬼怪抓到，你有 3 條命</li>
              <li>10分鐘內收集完所有點數晉級下一關</li>
            </ul>
          </div>
          <div class="leaderboard" id="leaderboardContent">
            <h3>排行榜</h3>
            <ol id="leaderboardList">
              <li>暫無記錄</li>
            </ol>
          </div>
        </div>
      </div>

      <div class="screen-overlay map-selection-screen" id="mapSelectionScreen">
        <h2>選擇地圖</h2>
        <div class="map-selector">
          <button
            class="map-button pacman-pixel-button active"
            data-map-index="0"
          >
            台北市中心
          </button>
          <button class="map-button pacman-pixel-button" data-map-index="1">
            台中市區
          </button>
          <button class="map-button pacman-pixel-button" data-map-index="2">
            高雄市區
          </button>
        </div>
        <button class="pacman-pixel-button" id="backToStartScreenBtn">
          返回主選單
        </button>
      </div>

      <div class="screen-overlay pause-screen" id="pauseScreen">
        <h2>遊戲暫停</h2>
        <button class="pacman-pixel-button" id="resumeGameBtn">繼續遊戲</button>
        <button class="pacman-pixel-button" id="pauseSettingsBtn">
          ⚙️ 遊戲設定
        </button>
        <button class="pacman-pixel-button" id="backToMenuBtnPause">
          回到主選單
        </button>
      </div>

      <div class="screen-overlay game-over-screen" id="gameOverScreen">
        <h2 id="gameOverTitle">遊戲結束</h2>
        <div class="ui-row" style="justify-content: center; margin: 20px 0">
          <span style="font-size: 2rem"
            >最終分數: <span id="finalScore">0</span></span
          >
        </div>
        <div
          id="newHighScore"
          style="
            display: none;
            color: #ffff00;
            font-size: 1.5rem;
            margin: 10px 0;
          "
        >
          🏆 新紀錄！
        </div>
        <button class="pacman-pixel-button" id="restartGameBtn">
          重新開始
        </button>
        <button class="pacman-pixel-button" id="backToMenuBtnGameOver">
          回到主選單
        </button>
      </div>

      <div id="minimap-container">
        <div id="minimap"></div>

        <div id="minimap-timer-overlay" class="minimap-overlay">
            <span id="minimap-timer-label">下次縮圈</span>
            <span id="minimap-timer-countdown">--:--</span>
        </div>
      </div>

      <div class="dev-console" id="devConsole">
        <div class="dev-console-output" id="devConsoleOutput">
          開發者指令視窗已啟用。輸入 'help' 查看可用指令。
        </div>
        <input
          type="text"
          class="dev-console-input"
          id="devConsoleInput"
          placeholder="輸入指令..."
        />
      </div>

      <!-- FPS 顯示 -->
      <div class="fps-overlay" id="fpsDisplay">FPS: --</div>

      <!-- 觸控狀態指示器 -->
      <div class="touch-indicator" id="touchIndicator">觸控模式</div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.8.49/Tone.min.js"></script>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script type="module" src="js/settings.js"></script>
    <script type="module" src="js/main.js"></script>
    <audio id="bgm" loop>
      <source src="audio/Pacmap_bgm.wav" type="audio/wav" />
    </audio>
  </body>
</html>
